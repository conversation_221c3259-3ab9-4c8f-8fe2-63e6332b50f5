from cube import CUBE
import neat
import os
import random

def converter(cube):
	conversion = []
	conversionMap = { '⬜' : 0, '🟧' : 1, '🟩' : 2, '🟥' : 3, '🟦' : 4, '🟨' : 5 }
	sides = [cube.up, cube.left, cube.front, cube.right, cube.back, cube.down]

	for side in sides:
		for i in range(3):
			for j in range(3):
				conversion.append(conversionMap[side[i][j]])

	return conversion

def fitness_scorer(cube):
	score = 0
	expectedValues = [0]*9 + [1]*9 + [2]*9 + [3]*9 + [4]*9 + [5]*9

	for x, y in zip(converter(cube), expectedValues):
		if x == y:
			score += 2
		'''else:
			score -= 1'''

	return score

def eval_genomes(genomes, config):
	moves = ["U", "U'", "U2", "L", "L'", "L2", "F", "F'", "F2", "R", "R'", "R2", "B", "B'", "B2", "D", "D'", "D2"]
	scramble = random.choices(moves, k=random.randint(10, 15))
	cubes = [CUBE(scramble) for _ in range(10)]

	for genome_id, genome in genomes:
		genome.fitness = fitness_scorer(cubes[0])
		net = neat.nn.FeedForwardNetwork.create(genome, config)

		for cube in cubes:
			output = net.activate(converter(cube))
			cube.turn(moves[output.index(max(output))])
			genome.fitness = fitness_scorer(cube) - 1

def run(config_file):
	config = neat.Config(neat.DefaultGenome, neat.DefaultReproduction, neat.DefaultSpeciesSet, neat.DefaultStagnation, config_file)
	p = neat.Population(config)
	p.add_reporter(neat.StdOutReporter(True))
	stats = neat.StatisticsReporter()
	p.add_reporter(stats)

	winner = p.run(eval_genomes, 100)
	input()
	print(f"\nBest genome:\n{winner}")

if __name__ == "__main__":
	local_dir = os.path.dirname(__file__)
	config_path = os.path.join(local_dir, 'config-feedforward')
	run(config_path)
