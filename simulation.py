import time

class CUBE:
	state = [0]*9 + [1]*9 + [2]*9 + [3]*9 + [4]*9 + [5]*9

	def __init__(self, scramble=[]):
		if scramble:
			for move in scramble:
				self.turn(move)
	
	def turn(self, move, debug=False):
		def clockwise(state, start):
			state[start], state[start + 2] = state[start + 2], state[start]
			state[start], state[start + 8] = state[start + 8], state[start]
			state[start], state[start + 6] = state[start + 6], state[start]
			state[start + 1], state[start + 5] = state[start + 5], state[start + 1]
			state[start + 1], state[start + 7] = state[start + 7], state[start + 1]
			state[start + 1], state[start + 3] = state[start + 3], state[start + 1]
			return state
		match move:
			case "U" | "U'" | "U2":
				for _ in range(3 if "'" in move else 2 if "2" in move else 1):
					self.state = clockwise(self.state, 0)
					self.state[9:12], self.state[18:21], self.state[27:30], self.state[36:39] = self.state[18:21], self.state[27:30], self.state[36:39], self.state[9:12]
			case "L" | "L'" | "L2":
				for _ in range(3 if "'" in move else 2 if "2" in move else 1):
					self.state = clockwise(self.state, 9)
					self.state[0], self.state[44], self.state[53], self.state[18] = self.state[44], self.state[53], self.state[18], self.state[0]
					self.state[3], self.state[41], self.state[50], self.state[21] = self.state[41], self.state[50], self.state[21], self.state[3]
					self.state[6], self.state[38], self.state[47], self.state[24] = self.state[38], self.state[47], self.state[24], self.state[6]
			case "F" | "F'" | "F2":
				for _ in range(3 if "'" in move else 2 if "2" in move else 1):
					self.state = clockwise(self.state, 18)
					self.state[6], self.state[17], self.state[51], self.state[27] = self.state[17], self.state[51], self.state[27], self.state[6]
					self.state[7], self.state[14], self.state[52], self.state[30] = self.state[14], self.state[52], self.state[30], self.state[7]
					self.state[8], self.state[11], self.state[53], self.state[33] = self.state[11], self.state[53], self.state[33], self.state[8]
			case "R" | "R'" | "R2":
				for _ in range(3 if "'" in move else 2 if "2" in move else 1):
					self.state = clockwise(self.state, 27)
					self.state[2], self.state[20], self.state[51], self.state[42] = self.state[20], self.state[51], self.state[42], self.state[2]
					self.state[5], self.state[23], self.state[48], self.state[39] = self.state[23], self.state[48], self.state[39], self.state[5]
					self.state[8], self.state[26], self.state[45], self.state[36] = self.state[26], self.state[45], self.state[36], self.state[8]
			case "B" | "B'" | "B2":
				for _ in range(3 if "'" in move else 2 if "2" in move else 1):
					self.state = clockwise(self.state, 36)
					self.state[0], self.state[29], self.state[45], self.state[15] = self.state[29], self.state[45], self.state[15], self.state[0]
					self.state[1], self.state[32], self.state[46], self.state[12] = self.state[32], self.state[46], self.state[12], self.state[1]
					self.state[2], self.state[35], self.state[47], self.state[9] = self.state[35], self.state[47], self.state[9], self.state[2]
			case "D" | "D'" | "D2":
				for _ in range(3 if "'" in move else 2 if "2" in move else 1):
					self.state = clockwise(self.state, 45)
					self.state[15:18], self.state[42:45], self.state[33:36], self.state[24:27] = self.state[42:45], self.state[33:36], self.state[24:27], self.state[15:18]

		if debug:
			time.sleep(0.5)
			print(move)
			self.pretty_print()

	def pretty_print(self):
		conversionMap = { 0 : '⬜', 1 : '🟧', 2 : '🟩', 3 : '🟥', 4 : '🟦', 5 : '🟨' }
		conversion = []
		i = 0

		for color in self.state:
			conversion.append(conversionMap[color])

		for color in conversion:
			i += 1
			print(color, end=" ")
			if i in [3, 6]:
				print()
			elif i == 9:
				print("\n")
				i = 0
		print("\n=========\n")


if __name__ == "__main__":
	cube = CUBE()
	for move in ["U", "L", "F", "R", "B", "D", "D'", "D2", "D2", "B'", "R'", "F'", "L'", "U'"]:
		cube.turn(move, debug=True)
