from simulation import CUBE
import neat
import os
import pickle
import random

best_so_far = float('-inf')

def fitness_scorer(cube):
	score = 0
	expectedValues = [0]*9 + [1]*9 + [2]*9 + [3]*9 + [4]*9 + [5]*9
	matching = sum(1 for x, y in zip(cube.state, expectedValues) if x == y)

	score += matching * 2

	for i in range(6):
		face = cube.state[i*9:(i+1)*9]
		if all(x == face[0] for x in face):
			score += 10

	top = cube.state[0*9:1*9]
	middle_faces = [1, 2, 3, 4]
	bottom = cube.state[5*9:6*9]

	if all(x == top[0] for x in top):
		score += 25

	if all(
		all(cube.state[f*9 + i] == cube.state[f*9] for i in [3, 4, 5])
		for f in middle_faces
	):
		score += 40

	if all(x == bottom[0] for x in bottom):
		score += 25

	solved = matching == 54
	if solved:
		score += 1000

	return score, solved

def eval_genomes(genomes, config):
	moves = ["U", "U'", "U2", "L", "L'", "L2", "F", "F'", "F2", "R", "R'", "R2", "B", "B'", "B2", "D", "D'", "D2"]

	for genome_id, genome in genomes:
		cube = CUBE(random.choices(moves, k=random.randint(10, 15)))
		genome.fitness = fitness_scorer(cube)
		net = neat.nn.FeedForwardNetwork.create(genome, config)

		for x in range(100):
			output = net.activate(cube.state)
			cube.turn(moves[output.index(max(output))])
			fitness, solved = fitness_scorer(cube)
			genome.fitness = fitness - (x / 10)

			global best_so_far

			if genome.fitness > best_so_far:
				with open("best_genome.pkl", "wb") as f:
					pickle.dump(genome, f)
				best_so_far = genome.fitness

			if solved:
				break

def run(config_file):
	config = neat.Config(neat.DefaultGenome, neat.DefaultReproduction, neat.DefaultSpeciesSet, neat.DefaultStagnation, config_file)
	p = neat.Population(config)
	p.add_reporter(neat.StdOutReporter(True))
	stats = neat.StatisticsReporter()
	p.add_reporter(stats)

	if os.path.exists("best_genome.pkl"):
		with open("best_genome.pkl", "rb") as f:
			best = pickle.load(f)
		p.population[best.key] = best

	winner = p.run(eval_genomes, 100)
	print(f"\nBest genome:\n{winner}")


if __name__ == "__main__":
	local_dir = os.path.dirname(__file__)
	config_path = os.path.join(local_dir, 'config-feedforward')
	run(config_path)
